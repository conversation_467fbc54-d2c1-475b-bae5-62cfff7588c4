kind: ConfigMap
apiVersion: v1
metadata:
  name: a2110-mail2incidents-config-map-#{appEnv}#
  namespace: a2110-olympus-monitoring
data:
  APP_FILE: src/mail2incidents/main.py
  DEBUG: "0"
  OLYMPUS_SERVICE_NAME: "#{olympusServiceName}#"
  LOGS_FOLDER: "/data/logs"

  # MS graph variables
  GRAPH_CLIENT_ID: "#{graphClientId}#"
  GRAPH_TENANT_ID: "#{graphTenantId}#"
  GRAPH_ENDPOINT_FMB: "#{graphFMBEndpoint}#"
  GRAPH_SCOPE: "#{graphScope}#"
  GRAPH_ENDPOINT: "#{graphEndpoint}#"
  GRAPH_EMAIL: "#{graphEmail}#"
  ID_INBOX: "#{graphInbox}#"
  ID_DESTINATION_FOLDER: "#{graphDestinationFolder}#"

  # SAP variables
  SAP_USER: "#{sapLogin}#"
  SAP_HOST: "#{sapHost}#"
  SAP_SERVICE_ROOT: "#{sapServiceRoot}#"
  SAP_INCIDENT_RESOURCE_PATH: "#{sapIncidentResourcePath}#"
  SAP_WORKCENTER: "#{sapWorkcenter}#"
  LONG_TEXT_FEEDBACK: "#{sapFeedbackLongText}#"
  CONFIRMATION_TEXT_FEEDBACK: "#{sapFeedbackConfirmationText}#"

  # APM variables
  ELASTIC_APM_SERVER_URL: "#{elasticApmServerUrl}#"
  ELASTIC_APM_SERVICE_NAME: "#{olympusServiceName}#"
  ELASTIC_APM_VERIFY_SERVER_CERT: "#{elasticApmVerifyServerCert}#"
  ENABLE_ELASTIC_APM: "#{enableElasticApm}#"
  ELASTIC_APM_ENVIRONMENT: "#{elasticApmEnvironment}#"
  ELASTIC_APM_LOG_LEVEL: "#{elasticApmLogLevel}#"

  # Auth variables
  JWK_URI: "#{jwkUri}#"
  JWK_VALID_AUDIENCES: >- 
    #{jwkValidAudiences}#
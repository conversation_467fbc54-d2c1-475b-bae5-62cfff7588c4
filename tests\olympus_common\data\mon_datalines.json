{"data": [{"input": {"type": "snmp_trap", "host": "**************", "SNMPv2-MIB::sysUpTime.0": "385 days, 01:43:15.08", "@timestamp": "2024-07-05T17:13:17.142423542Z", "SNMPv2-MIB::snmpTrapOID.0": "SNMPv2-SMI::enterprises.776.50.645.***********.11", "message": "#<SNMP::SNMPv2_Trap:0x54bf86 @varbind_list=[#<SNMP::VarBind:0x67e28d53 @name=[*******.*******.0], @value=#<SNMP::TimeTicks:0x44f77c6e @value=3327019508>>, #<SNMP::VarBind:0x617a53f9 @name=[*******.*******.4.1.0], @value=[*******.4.1.776.50.645.***********.11]>, #<SNMP::VarBind:0x6004a831 @name=[*******.*******.1.1.0], @value=#<SNMP::Integer:0x13ff2977 @value=0>>, #<SNMP::VarBind:0x6c14236a @name=[*******.4.1.776.********], @value=#<SNMP::Integer:0x5f42508a @value=0>>, #<SNMP::VarBind:0x1ac22e73 @name=[*******.4.1.776.11.10.0], @value=\"aster5.alarmInfo.currentAlarms.alarmSyncLoss on\">, #<SNMP::VarBind:0x701c08cd @name=[*******.4.1.776.11.11.0], @value=#<SNMP::Integer:0x3dde7ad7 @value=4>>, #<SNMP::VarBind:0x70d5abe6 @name=[*******.4.1.776.11.12.0], @value=#<SNMP::Integer:0x7b6fa239 @value=4>>], @request_id=1368397431, @error_index=0, @error_status=0, @source_ip=\"**************\">", "@version": "1", "event.logstash.instance_name": "iictmiapls016", "event.kafka.partition": 0, "event.kafka.offset": 74540, "event.kafka.key": null, "event.kafka.timestamp": "2024-07-05T17:13:17.271Z", "event.kafka.topic": "a1298-modem_management-telindus_datalines_events-prd", "event.kafka.consumer_group": "a1559-logstash-a1298-modem_management-telindus_datalines_events-prd", "event.uuid": "dd7f264b-f225-4cbd-b65f-e97f0baaa6ad", "snmptrap.tlsSeverityLevel": "4", "snmptrap.tlsTrapDescription": "aster5.alarmInfo.currentAlarms.alarmSyncLoss on", "snmptrap.ifIndex0": "0", "snmptrap.tlsTrapSeverityLevel": "4", "snmptrap.tlsCntTreeGlobalIndex": "0", "snmptrap.ifIndex1": null, "IF-MIB::ifIndex.2": null}, "output": {"agent_id": 0, "clear_type": "automatic", "manager": "mon-datalines", "action_class": "TE", "metric_type": "", "top_level": "A1298", "event_id": "", "handle_time": "2024-01-01 12:00:01", "ci_id": "dummy<PERSON>t", "node": "**************", "node_alias": "**************", "raise_time": "2024-07-05 17:13:17", "metric_name": "alarmSyncLoss", "summary": "aster5.alarmInfo.currentAlarms.alarmSyncLoss on", "additional_data": "{\"responsible_department\": \"S<PERSON>\"}", "event_type": "problem", "clear_time": null, "severity": 5, "actionable": true, "wake_up_time": "2024-07-05 17:15:17"}, "ignore_in_batch": "Problem ignored because clear is on the same raise_time"}, {"input": {"type": "snmp_trap", "host": "**************", "SNMPv2-MIB::sysUpTime.0": "385 days, 01:43:15.08", "@timestamp": "2024-07-05T17:13:17.154472392Z", "SNMPv2-MIB::snmpTrapOID.0": "SNMPv2-SMI::enterprises.776.50.645.***********.11", "message": "#<SNMP::SNMPv2_Trap:0x60936bba @varbind_list=[#<SNMP::VarBind:0x2397be2d @name=[*******.*******.0], @value=#<SNMP::TimeTicks:0x32268f5a @value=3327019508>>, #<SNMP::VarBind:0x4993fc1f @name=[*******.*******.4.1.0], @value=[*******.4.1.776.50.645.***********.11]>, #<SNMP::VarBind:0x36a04ed1 @name=[*******.*******.1.1.0], @value=#<SNMP::Integer:0x28d8e8fe @value=0>>, #<SNMP::VarBind:0x2e82fee0 @name=[*******.4.1.776.********], @value=#<SNMP::Integer:0x78279c2b @value=0>>, #<SNMP::VarBind:0x4c458338 @name=[*******.4.1.776.11.10.0], @value=\"aster5.alarmInfo.currentAlarms.alarmSyncLoss off\">, #<SNMP::VarBind:0x23b51d56 @name=[*******.4.1.776.11.11.0], @value=#<SNMP::Integer:0x1a79823e @value=0>>, #<SNMP::VarBind:0x5efb839e @name=[*******.4.1.776.11.12.0], @value=#<SNMP::Integer:0x3e47973a @value=0>>], @request_id=1368397435, @error_index=0, @error_status=0, @source_ip=\"**************\">", "@version": "1", "event.logstash.instance_name": "iictmiapls016", "event.kafka.partition": 0, "event.kafka.offset": 74544, "event.kafka.key": null, "event.kafka.timestamp": "2024-07-05T17:13:17.271Z", "event.kafka.topic": "a1298-modem_management-telindus_datalines_events-prd", "event.kafka.consumer_group": "a1559-logstash-a1298-modem_management-telindus_datalines_events-prd", "event.uuid": "7cbd0aa0-9091-4ed2-aa3d-925900bbcd1e", "snmptrap.tlsSeverityLevel": "0", "snmptrap.tlsTrapDescription": "aster5.alarmInfo.currentAlarms.alarmSyncLoss off", "snmptrap.ifIndex0": "0", "snmptrap.tlsTrapSeverityLevel": "0", "snmptrap.tlsCntTreeGlobalIndex": "0", "snmptrap.ifIndex1": null, "IF-MIB::ifIndex.2": null}, "output": {"agent_id": 0, "clear_type": "automatic", "manager": "mon-datalines", "action_class": "TE", "metric_type": "", "top_level": "A1298", "event_id": "", "handle_time": "2024-01-01 12:00:01", "ci_id": "dummy<PERSON>t", "node": "**************", "node_alias": "**************", "raise_time": "2024-07-05 17:13:17", "metric_name": "alarmSyncLoss", "summary": "aster5.alarmInfo.currentAlarms.alarmSyncLoss off", "additional_data": "{\"responsible_department\": \"S<PERSON>\"}", "event_type": "clear", "clear_time": "2024-07-05 17:13:17", "severity": 1, "actionable": false, "wake_up_time": "2024-07-05 17:15:17"}}, {"input": {"type": "snmp_trap", "host": "**************", "SNMPv2-MIB::sysUpTime.0": "216 days, 13:15:07.16", "@timestamp": "2024-07-06T23:40:34.894864673Z", "SNMPv2-MIB::snmpTrapOID.0": "SNMPv2-SMI::enterprises.776.50.645.***********.10", "message": "#<SNMP::SNMPv2_Trap:0x181b978f @varbind_list=[#<SNMP::VarBind:0x39d04db7 @name=[*******.*******.0], @value=#<SNMP::TimeTicks:0x6fe255db @value=1871010716>>, #<SNMP::VarBind:0x2b010047 @name=[*******.*******.4.1.0], @value=[*******.4.1.776.50.645.***********.10]>, #<SNMP::VarBind:0x763dd79c @name=[*******.*******.1.1.0], @value=#<SNMP::Integer:0x4dbb9119 @value=0>>, #<SNMP::VarBind:0x300a59f8 @name=[*******.4.1.776.********], @value=#<SNMP::Integer:0x13aa7486 @value=0>>, #<SNMP::VarBind:0xbb3c058 @name=[*******.4.1.776.11.10.0], @value=\"aster5.alarmInfo.currentAlarms.notResponding off\">, #<SNMP::VarBind:0x1a11bb6e @name=[*******.4.1.776.11.11.0], @value=#<SNMP::Integer:0x7836e44d @value=0>>, #<SNMP::VarBind:0x3878b189 @name=[*******.4.1.776.11.12.0], @value=#<SNMP::Integer:0x2ddc17de @value=0>>], @request_id=892339243, @error_index=0, @error_status=0, @source_ip=\"**************\">", "@version": "1", "event.logstash.instance_name": "iictmiapls016", "event.kafka.partition": 0, "event.kafka.offset": 74547, "event.kafka.key": null, "event.kafka.timestamp": "2024-07-06T23:40:34.998Z", "event.kafka.topic": "a1298-modem_management-telindus_datalines_events-prd", "event.kafka.consumer_group": "a1559-logstash-a1298-modem_management-telindus_datalines_events-prd", "event.uuid": "6324cc5e-80a0-4dc3-a312-42f743d809be", "snmptrap.tlsSeverityLevel": "0", "snmptrap.tlsTrapDescription": "aster5.alarmInfo.currentAlarms.notResponding off", "snmptrap.ifIndex0": "0", "snmptrap.tlsTrapSeverityLevel": "0", "snmptrap.tlsCntTreeGlobalIndex": "0", "snmptrap.ifIndex1": null, "IF-MIB::ifIndex.2": null}, "output": {"agent_id": 0, "clear_type": "automatic", "manager": "mon-datalines", "action_class": "TE", "metric_type": "", "top_level": "A1298", "event_id": "", "handle_time": "2024-01-01 12:00:01", "ci_id": "dummy<PERSON>t", "node": "**************", "node_alias": "**************", "raise_time": "2024-07-06 23:40:34", "metric_name": "notResponding", "summary": "aster5.alarmInfo.currentAlarms.notResponding off", "additional_data": "{\"responsible_department\": \"S<PERSON>\"}", "event_type": "clear", "clear_time": "2024-07-06 23:40:34", "severity": 1, "actionable": false, "wake_up_time": "2024-07-06 23:42:34"}}, {"input": {"type": "snmp_trap", "host": "**************", "SNMPv2-MIB::sysUpTime.0": "216 days, 13:15:07.16", "@timestamp": "2024-07-06T23:40:34.895114808Z", "SNMPv2-MIB::snmpTrapOID.0": "SNMPv2-SMI::enterprises.776.50.645.***********.15", "message": "#<SNMP::SNMPv2_Trap:0x75da9c51 @varbind_list=[#<SNMP::VarBind:0x457c1ba9 @name=[*******.*******.0], @value=#<SNMP::TimeTicks:0x77d18fc5 @value=1871010716>>, #<SNMP::VarBind:0x5648ad7a @name=[*******.*******.4.1.0], @value=[*******.4.1.776.50.645.***********.15]>, #<SNMP::VarBind:0x42735766 @name=[*******.*******.1.1.0], @value=#<SNMP::Integer:0x6777add2 @value=0>>, #<SNMP::VarBind:0x1c23b95e @name=[*******.4.1.776.********], @value=#<SNMP::Integer:0xf93e4dd @value=0>>, #<SNMP::VarBind:0x57835a2e @name=[*******.4.1.776.11.10.0], @value=\"aster5.alarmInfo.currentAlarms.powerSupply1CvFail off\">, #<SNMP::VarBind:0xdc07431 @name=[*******.4.1.776.11.11.0], @value=#<SNMP::Integer:0xc28d59e @value=0>>, #<SNMP::VarBind:0x1c8d6dca @name=[*******.4.1.776.11.12.0], @value=#<SNMP::Integer:0x5192165f @value=0>>], @request_id=892339244, @error_index=0, @error_status=0, @source_ip=\"**************\">", "@version": "1", "event.logstash.instance_name": "iictmiapls016", "event.kafka.partition": 0, "event.kafka.offset": 74548, "event.kafka.key": null, "event.kafka.timestamp": "2024-07-06T23:40:34.998Z", "event.kafka.topic": "a1298-modem_management-telindus_datalines_events-prd", "event.kafka.consumer_group": "a1559-logstash-a1298-modem_management-telindus_datalines_events-prd", "event.uuid": "28657392-a43b-46c5-bed1-6f5cc3ec33fb", "snmptrap.tlsSeverityLevel": "0", "snmptrap.tlsTrapDescription": "aster5.alarmInfo.currentAlarms.powerSupply1CvFail off", "snmptrap.ifIndex0": "0", "snmptrap.tlsTrapSeverityLevel": "0", "snmptrap.tlsCntTreeGlobalIndex": "0", "snmptrap.ifIndex1": null, "IF-MIB::ifIndex.2": null}, "output": {"agent_id": 0, "clear_type": "automatic", "manager": "mon-datalines", "action_class": "TE", "metric_type": "", "top_level": "A1298", "event_id": "", "handle_time": "2024-01-01 12:00:01", "ci_id": "dummy<PERSON>t", "node": "**************", "node_alias": "**************", "raise_time": "2024-07-06 23:40:34", "metric_name": "powerSupply1CvFail", "summary": "aster5.alarmInfo.currentAlarms.powerSupply1CvFail off", "additional_data": "{\"responsible_department\": \"S<PERSON>\"}", "event_type": "clear", "clear_time": "2024-07-06 23:40:34", "severity": 1, "actionable": false, "wake_up_time": "2024-07-06 23:42:34"}}, {"input": {"type": "snmp_trap", "host": "**************", "SNMPv2-MIB::sysUpTime.0": "216 days, 13:15:07.16", "@timestamp": "2024-07-06T23:40:34.896881016Z", "SNMPv2-MIB::snmpTrapOID.0": "SNMPv2-SMI::enterprises.776.50.645.***********.16", "message": "#<SNMP::SNMPv2_Trap:0x15332be4 @varbind_list=[#<SNMP::VarBind:0x23463550 @name=[*******.*******.0], @value=#<SNMP::TimeTicks:0x10055019 @value=1871010716>>, #<SNMP::VarBind:0x1ce535f6 @name=[*******.*******.4.1.0], @value=[*******.4.1.776.50.645.***********.16]>, #<SNMP::VarBind:0x41a26380 @name=[*******.*******.1.1.0], @value=#<SNMP::Integer:0x53dc2251 @value=0>>, #<SNMP::VarBind:0x696335ab @name=[*******.4.1.776.********], @value=#<SNMP::Integer:0x5c9cf7f4 @value=0>>, #<SNMP::VarBind:0xef03184 @name=[*******.4.1.776.11.10.0], @value=\"aster5.alarmInfo.currentAlarms.powerSupply2CvFail off\">, #<SNMP::VarBind:0x3c8c54a1 @name=[*******.4.1.776.11.11.0], @value=#<SNMP::Integer:0x4c32e0f6 @value=0>>, #<SNMP::VarBind:0x772a8dd3 @name=[*******.4.1.776.11.12.0], @value=#<SNMP::Integer:0x78d9fea3 @value=0>>], @request_id=892339245, @error_index=0, @error_status=0, @source_ip=\"**************\">", "@version": "1", "event.logstash.instance_name": "iictmiapls016", "event.kafka.partition": 0, "event.kafka.offset": 74549, "event.kafka.key": null, "event.kafka.timestamp": "2024-07-06T23:40:34.998Z", "event.kafka.topic": "a1298-modem_management-telindus_datalines_events-prd", "event.kafka.consumer_group": "a1559-logstash-a1298-modem_management-telindus_datalines_events-prd", "event.uuid": "986490d5-8d24-476c-8223-4c40e3bdbb1b", "snmptrap.tlsSeverityLevel": "0", "snmptrap.tlsTrapDescription": "aster5.alarmInfo.currentAlarms.powerSupply2CvFail off", "snmptrap.ifIndex0": "0", "snmptrap.tlsTrapSeverityLevel": "0", "snmptrap.tlsCntTreeGlobalIndex": "0", "snmptrap.ifIndex1": null, "IF-MIB::ifIndex.2": null}, "output": {"agent_id": 0, "clear_type": "automatic", "manager": "mon-datalines", "action_class": "TE", "metric_type": "", "top_level": "A1298", "event_id": "", "handle_time": "2024-01-01 12:00:01", "ci_id": "dummy<PERSON>t", "node": "**************", "node_alias": "**************", "raise_time": "2024-07-06 23:40:34", "metric_name": "powerSupply2CvFail", "summary": "aster5.alarmInfo.currentAlarms.powerSupply2CvFail off", "additional_data": "{\"responsible_department\": \"S<PERSON>\"}", "event_type": "clear", "clear_time": "2024-07-06 23:40:34", "severity": 1, "actionable": false, "wake_up_time": "2024-07-06 23:42:34"}}, {"input": {"type": "snmp_trap", "host": "**************", "SNMPv2-MIB::sysUpTime.0": "229 days, 06:36:41.24", "@timestamp": "2024-07-07T09:44:49.966541705Z", "SNMPv2-MIB::snmpTrapOID.0": "SNMPv2-SMI::enterprises.776.39.641.***********.11", "message": "#<SNMP::SNMPv2_Trap:0x1cd3da34 @varbind_list=[#<SNMP::VarBind:0x6196414e @name=[*******.*******.0], @value=#<SNMP::TimeTicks:0x5a2cf6 @value=1980940124>>, #<SNMP::VarBind:0x25a44b3 @name=[*******.*******.4.1.0], @value=[*******.4.1.776.39.641.***********.11]>, #<SNMP::VarBind:0x5aa64b06 @name=[*******.*******.1.1.1], @value=#<SNMP::Integer:0x9e9fd2d @value=1>>, #<SNMP::VarBind:0x58855d7a @name=[*******.4.1.776.********], @value=#<SNMP::Integer:0x8aaf505 @value=0>>, #<SNMP::VarBind:0x60bc716d @name=[*******.4.1.776.11.10.0], @value=\"aster5/line.alarmInfo.currentAlarms.retrain on\">, #<SNMP::VarBind:0x26faf8c2 @name=[*******.4.1.776.11.11.0], @value=#<SNMP::Integer:0x54e60fea @value=2>>, #<SNMP::VarBind:0xbbbcaae @name=[*******.4.1.776.11.12.0], @value=#<SNMP::Integer:0x88ff5ad @value=2>>], @request_id=892339249, @error_index=0, @error_status=0, @source_ip=\"**************\">", "@version": "1", "event.logstash.instance_name": "iictmiapls016", "event.kafka.partition": 0, "event.kafka.offset": 74553, "event.kafka.key": null, "event.kafka.timestamp": "2024-07-07T09:44:50.068Z", "event.kafka.topic": "a1298-modem_management-telindus_datalines_events-prd", "event.kafka.consumer_group": "a1559-logstash-a1298-modem_management-telindus_datalines_events-prd", "event.uuid": "ed586099-24b7-40cd-9f3f-bf4e65e2fe17", "snmptrap.tlsSeverityLevel": "2", "snmptrap.tlsTrapDescription": "aster5/line.alarmInfo.currentAlarms.retrain on", "snmptrap.ifIndex0": null, "snmptrap.tlsTrapSeverityLevel": "2", "snmptrap.tlsCntTreeGlobalIndex": "0", "snmptrap.ifIndex1": "1", "IF-MIB::ifIndex.2": null}, "output": {"agent_id": 0, "clear_type": "automatic", "manager": "mon-datalines", "action_class": "TE", "metric_type": "", "top_level": "A1298", "event_id": "", "handle_time": "2024-01-01 12:00:01", "ci_id": "dummy<PERSON>t", "node": "**************", "node_alias": "**************", "raise_time": "2024-07-07 09:44:49", "metric_name": "retrain", "summary": "aster5/line.alarmInfo.currentAlarms.retrain on", "additional_data": "{\"responsible_department\": \"S<PERSON>\"}", "event_type": "problem", "clear_time": null, "severity": 3, "actionable": false, "wake_up_time": "2024-07-07 09:46:49"}, "ignore_in_batch": "Problem ignored because clear is on the same raise_time"}, {"input": {"type": "snmp_trap", "host": "**************", "SNMPv2-MIB::sysUpTime.0": "229 days, 06:37:09.24", "@timestamp": "2024-07-07T09:44:49.966794471Z", "SNMPv2-MIB::snmpTrapOID.0": "SNMPv2-SMI::enterprises.776.39.641.***********.11", "message": "#<SNMP::SNMPv2_Trap:0x1dbfe91b @varbind_list=[#<SNMP::VarBind:0x3fd10888 @name=[*******.*******.0], @value=#<SNMP::TimeTicks:0x577fd964 @value=1980942924>>, #<SNMP::VarBind:0x356a15a1 @name=[*******.*******.4.1.0], @value=[*******.4.1.776.39.641.***********.11]>, #<SNMP::VarBind:0x58f68cca @name=[*******.*******.1.1.1], @value=#<SNMP::Integer:0x6f544a14 @value=1>>, #<SNMP::VarBind:0x46491a63 @name=[*******.4.1.776.********], @value=#<SNMP::Integer:0x236bc816 @value=0>>, #<SNMP::VarBind:0x4f97906d @name=[*******.4.1.776.11.10.0], @value=\"aster5/line.alarmInfo.currentAlarms.retrain off\">, #<SNMP::VarBind:0x2de6761 @name=[*******.4.1.776.11.11.0], @value=#<SNMP::Integer:0xc4aef5b @value=0>>, #<SNMP::VarBind:0x719cadbc @name=[*******.4.1.776.11.12.0], @value=#<SNMP::Integer:0x2fb4f1a @value=0>>], @request_id=892339250, @error_index=0, @error_status=0, @source_ip=\"**************\">", "@version": "1", "event.logstash.instance_name": "iictmiapls016", "event.kafka.partition": 0, "event.kafka.offset": 74554, "event.kafka.key": null, "event.kafka.timestamp": "2024-07-07T09:44:50.069Z", "event.kafka.topic": "a1298-modem_management-telindus_datalines_events-prd", "event.kafka.consumer_group": "a1559-logstash-a1298-modem_management-telindus_datalines_events-prd", "event.uuid": "4275abe0-8665-4a75-a9ab-8adc71f14f51", "snmptrap.tlsSeverityLevel": "0", "snmptrap.tlsTrapDescription": "aster5/line.alarmInfo.currentAlarms.retrain off", "snmptrap.ifIndex0": null, "snmptrap.tlsTrapSeverityLevel": "0", "snmptrap.tlsCntTreeGlobalIndex": "0", "snmptrap.ifIndex1": "1", "IF-MIB::ifIndex.2": null}, "output": {"agent_id": 0, "clear_type": "automatic", "manager": "mon-datalines", "action_class": "TE", "metric_type": "", "top_level": "A1298", "event_id": "", "handle_time": "2024-01-01 12:00:01", "ci_id": "dummy<PERSON>t", "node": "**************", "node_alias": "**************", "raise_time": "2024-07-07 09:44:49", "metric_name": "retrain", "summary": "aster5/line.alarmInfo.currentAlarms.retrain off", "additional_data": "{\"responsible_department\": \"S<PERSON>\"}", "event_type": "clear", "clear_time": "2024-07-07 09:44:49", "severity": 1, "actionable": false, "wake_up_time": "2024-07-07 09:46:49"}}, {"input": {"type": "snmp_trap", "host": "**************", "SNMPv2-MIB::sysUpTime.0": "431 days, 21:04:15.12", "@timestamp": "2024-07-11T05:40:58.355884624Z", "SNMPv2-MIB::snmpTrapOID.0": "SNMPv2-SMI::enterprises.776.39.640.***********.10", "message": "#<SNMP::SNMPv2_Trap:0x6fc96703 @varbind_list=[#<SNMP::VarBind:0xcd5e0aa @name=[*******.*******.0], @value=#<SNMP::TimeTicks:0xe832910 @value=3731425512>>, #<SNMP::VarBind:0x2a56a6bc @name=[*******.*******.4.1.0], @value=[*******.4.1.776.39.640.***********.10]>, #<SNMP::VarBind:0x796328f3 @name=[*******.*******.1.1.0], @value=#<SNMP::Integer:0x5a9057c5 @value=0>>, #<SNMP::VarBind:0x4d4c2e2e @name=[*******.4.1.776.********], @value=#<SNMP::Integer:0x52b0127d @value=0>>, #<SNMP::VarBind:0x32748c68 @name=[*******.4.1.776.11.10.0], @value=\"aster5.alarmInfo.currentAlarms.notResponding off\">, #<SNMP::VarBind:0x6a02cb9c @name=[*******.4.1.776.11.11.0], @value=#<SNMP::Integer:0x4693b5ac @value=0>>, #<SNMP::VarBind:0x146264f9 @name=[*******.4.1.776.11.12.0], @value=#<SNMP::Integer:0x263add1 @value=0>>], @request_id=1529875825, @error_index=0, @error_status=0, @source_ip=\"**************\">", "@version": "1", "event.logstash.instance_name": "iictmiapls016", "event.kafka.partition": 0, "event.kafka.offset": 74573, "event.kafka.key": null, "event.kafka.timestamp": "2024-07-11T05:40:58.458Z", "event.kafka.topic": "a1298-modem_management-telindus_datalines_events-prd", "event.kafka.consumer_group": "a1559-logstash-a1298-modem_management-telindus_datalines_events-prd", "event.uuid": "a20f1814-1da9-4ed4-8c20-7600cf5d01f4", "snmptrap.tlsSeverityLevel": "0", "snmptrap.tlsTrapDescription": "aster5.alarmInfo.currentAlarms.notResponding off", "snmptrap.ifIndex0": "0", "snmptrap.tlsTrapSeverityLevel": "0", "snmptrap.tlsCntTreeGlobalIndex": "0", "snmptrap.ifIndex1": null, "IF-MIB::ifIndex.2": null}, "output": {"agent_id": 0, "clear_type": "automatic", "manager": "mon-datalines", "action_class": "TE", "metric_type": "", "top_level": "A1298", "event_id": "", "handle_time": "2024-01-01 12:00:01", "ci_id": "dummy<PERSON>t", "node": "**************", "node_alias": "**************", "raise_time": "2024-07-11 05:40:58", "metric_name": "notResponding", "summary": "aster5.alarmInfo.currentAlarms.notResponding off", "additional_data": "{\"responsible_department\": \"S<PERSON>\"}", "event_type": "clear", "clear_time": "2024-07-11 05:40:58", "severity": 1, "actionable": false, "wake_up_time": "2024-07-11 05:42:58"}}, {"input": {"type": "snmp_trap", "host": "**************", "SNMPv2-MIB::sysUpTime.0": "382 days, 13:25:48.04", "@timestamp": "2024-07-12T03:01:55.070628670Z", "SNMPv2-MIB::snmpTrapOID.0": "SNMPv2-MIB::snmp", "message": "#<SNMP::SNMPv2_Trap:0x3151f81a @varbind_list=[#<SNMP::VarBind:0x591f7e92 @name=[*******.*******.0], @value=#<SNMP::TimeTicks:0x684a7448 @value=3305314804>>, #<SNMP::VarBind:0x5665c073 @name=[*******.*******.4.1.0], @value=[*******.2.1.11]>, #<SNMP::VarBind:0x759578a0 @name=[*******.*******.1.1.2], @value=#<SNMP::Integer:0x57dc636e @value=2>>], @request_id=1764754954, @error_index=0, @error_status=0, @source_ip=\"**************\">", "@version": "1", "event.logstash.instance_name": "iictmiapls016", "event.kafka.partition": 0, "event.kafka.offset": 74586, "event.kafka.key": null, "event.kafka.timestamp": "2024-07-12T03:01:55.171Z", "event.kafka.topic": "a1298-modem_management-telindus_datalines_events-prd", "event.kafka.consumer_group": "a1559-logstash-a1298-modem_management-telindus_datalines_events-prd", "event.uuid": "016b1402-9d12-4a74-830f-f476091dc8cb", "snmptrap.tlsSeverityLevel": null, "snmptrap.tlsTrapDescription": null, "snmptrap.ifIndex0": null, "snmptrap.tlsTrapSeverityLevel": null, "snmptrap.tlsCntTreeGlobalIndex": null, "snmptrap.ifIndex1": null, "IF-MIB::ifIndex.2": "2"}, "output": {"agent_id": 0, "clear_type": "automatic", "manager": "mon-datalines", "action_class": "TE", "metric_type": "", "top_level": "A1298", "event_id": "", "handle_time": "2024-01-01 12:00:01", "ci_id": "dummy<PERSON>t", "node": "**************", "node_alias": "**************", "raise_time": "2024-07-12 03:01:55", "metric_name": "None", "additional_data": "{\"responsible_department\": \"N/A\"}", "event_type": "problem", "clear_time": null, "severity": 5, "actionable": false, "wake_up_time": "2024-07-12 03:03:55"}}]}